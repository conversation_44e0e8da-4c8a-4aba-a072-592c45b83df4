#---------JWT---
JWT_ACCESS_SECRET_KEY=123
JWT_REFRESH_SECRET_KEY=123
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRED=60
REFRESH_TOKEN_EXPIRED=10080

#---------database---
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=hr_system
DATABASE_USER=postgres
DATABASE_PASSWORD=Aa12345
DATABASE_CONN_POOL_SIZE=10

#--------logger-------
LOGGER_FORMAT="%(asctime)s - %(module)s - %(levelname)s - %(message)s"
LOGGER_LEVEL=INFO
SQLALCHEMY_LOG_LEVEL=INFO

#----------CORS allow domain-----
CORS_ALLOWED_ORIGINS=http://localhost:8000

#----------pagination-------
DEFAULT_PAGE_SIZE=100

#---------Google------
GMAIL_API_KEY=
GOOGLE_DRIVER_API_KEY=

API_PORT=8000
API_SECRET_KEY=your_secret_key_here

#---------Environment-------
ENVIRONMENT=development

#---------Google API-------
GOOGLE_API_KEY=

#---------Babel-------
BABEL_DEFAULT_LOCALE=en

#---------Timezone-------
DEFAULT_TIMEZONE_SERVER=UTC
DEFAULT_TIMEZONE_USER=Asia/Tokyo

#---------Upload-------
UPLOAD_CLOUD_TARGET=false
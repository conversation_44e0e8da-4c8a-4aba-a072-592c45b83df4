"""add_jwt_tokens_table

Revision ID: c45d1c3adbf6
Revises: 37062e6f076e
Create Date: 2025-09-16 13:51:55.734287

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c45d1c3adbf6'
down_revision: Union[str, None] = '37062e6f076e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('t_jwt_tokens',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('token_type', sa.String(length=20), nullable=False),
    sa.Column('token_hash', sa.String(length=255), nullable=False),
    sa.Column('jti', sa.String(length=36), nullable=False),
    sa.Column('is_blacklisted', sa.Boolean(), nullable=False),
    sa.Column('expires_at', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['t_users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('jti'),
    sa.UniqueConstraint('token_hash')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('t_jwt_tokens')
    # ### end Alembic commands ###

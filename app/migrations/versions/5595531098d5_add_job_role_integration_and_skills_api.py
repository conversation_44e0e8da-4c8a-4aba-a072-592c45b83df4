"""add_job_role_integration_and_skills_api

Revision ID: 5595531098d5
Revises: 047145171e1d
Create Date: 2025-09-16 07:35:30.029931

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5595531098d5'
down_revision: Union[str, None] = '047145171e1d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('t_jobs', sa.Column('job_role_id', sa.Integer(), nullable=True, comment='Reference to job role master data'))
    op.create_foreign_key(None, 't_jobs', 'm_job_roles', ['job_role_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by <PERSON>embic - please adjust! ###
    op.drop_constraint(None, 't_jobs', type_='foreignkey')
    op.drop_column('t_jobs', 'job_role_id')
    # ### end Alembic commands ###

"""add_job_role_id_to_skills_and_remove_unique_constraint

Revision ID: 3b34960112f7
Revises: 5595531098d5
Create Date: 2025-09-16 08:10:01.990915

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3b34960112f7'
down_revision: Union[str, None] = '5595531098d5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('m_skills', sa.Column('job_role_id', sa.Integer(), nullable=True, comment='Reference to job role master data'))
    op.drop_constraint('m_skills_name_key', 'm_skills', type_='unique')
    op.create_foreign_key(None, 'm_skills', 'm_job_roles', ['job_role_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'm_skills', type_='foreignkey')
    op.create_unique_constraint('m_skills_name_key', 'm_skills', ['name'])
    op.drop_column('m_skills', 'job_role_id')
    # ### end Alembic commands ###

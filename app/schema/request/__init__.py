from .user_schemas import Use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UserUpdateRequest
from .role_schemas import RoleCreateRequest, RoleUpdateRequest
from .certificate_schemas import CertificateCreateRequest, CertificateUpdateRequest
from .black_list_schemas import BlackList<PERSON>reateRequest, Black<PERSON><PERSON>UpdateRequest
from .job_schemas import JobC<PERSON><PERSON><PERSON><PERSON>, Job<PERSON>pdateRequest
from .job_requirement_schemas import JobRequirementCreateRequest, JobRequirementUpdateRequest
from .candidate_schemas import CandidateCreateRequest, CandidateUpdateRequest
from .resume_schemas import ResumeCreateRequest, ResumeUpdateRequest
from .application_schemas import ApplicationCreateRequest, ApplicationUpdateRequest
from .job_requirement_certificate_schemas import JobRequirement<PERSON>ertificateCreateRequest, JobRequirementCertificateUpdateRequest
from .job_requirement_black_list_schemas import JobRequirementBlackListCreateRequest, JobRequirementBlackListUpdateRequest